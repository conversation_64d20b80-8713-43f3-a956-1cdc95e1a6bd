// 运维管理页面数据和交互逻辑

$axure.loadCurrentPage({
  "url": "运维.html",
  "generationDate": new Date(1640995200000),
  "isCanvasEnabled": false,
  "variables": ["OnLoadVariable"],
  "page": {
    "packageId": "运维管理",
    "type": "Axure:Page",
    "name": "运维",
    "notes": {
      "pageName": "运维管理页面",
      "description": "系统参数配置界面，支持远程运维操作"
    },
    "style": {
      "baseStyle": "627587b6038d43cca051c114ac41ad32",
      "pageAlignment": "near",
      "fill": {
        "fillType": "solid",
        "color": 0xFFFFFF
      },
      "image": null,
      "imageHorizontalAlignment": "near",
      "imageVerticalAlignment": "near",
      "imageRepeat": "auto",
      "favicon": null,
      "sketchFactor": "0",
      "colorStyle": "appliedColor",
      "fontName": "Applied Font",
      "fontSize": "Applied Font"
    },
    "adaptiveStyles": {},
    "interactionMap": {
      // 菜单项点击事件
      "config_item1": {
        "onClick": [
          {
            "description": "显示基础参数配置",
            "cases": [
              {
                "condition": "true",
                "isNewIfGroup": false,
                "actions": [
                  {
                    "action": "setActiveMenuItem",
                    "target": "config_item1"
                  },
                  {
                    "action": "showPanel",
                    "target": "basic_config_panel"
                  }
                ]
              }
            ]
          }
        ]
      },
      "config_item2": {
        "onClick": [
          {
            "description": "显示数据库配置",
            "cases": [
              {
                "condition": "true",
                "isNewIfGroup": false,
                "actions": [
                  {
                    "action": "setActiveMenuItem",
                    "target": "config_item2"
                  },
                  {
                    "action": "showPanel",
                    "target": "database_config_panel"
                  }
                ]
              }
            ]
          }
        ]
      },
      "config_item3": {
        "onClick": [
          {
            "description": "显示网络配置",
            "cases": [
              {
                "condition": "true",
                "isNewIfGroup": false,
                "actions": [
                  {
                    "action": "setActiveMenuItem",
                    "target": "config_item3"
                  },
                  {
                    "action": "showPanel",
                    "target": "network_config_panel"
                  }
                ]
              }
            ]
          }
        ]
      },
      "config_item4": {
        "onClick": [
          {
            "description": "显示安全配置",
            "cases": [
              {
                "condition": "true",
                "isNewIfGroup": false,
                "actions": [
                  {
                    "action": "setActiveMenuItem",
                    "target": "config_item4"
                  },
                  {
                    "action": "showPanel",
                    "target": "security_config_panel"
                  }
                ]
              }
            ]
          }
        ]
      },
      // 运维操作菜单
      "ops_item1": {
        "onClick": [
          {
            "description": "显示服务管理",
            "cases": [
              {
                "condition": "true",
                "isNewIfGroup": false,
                "actions": [
                  {
                    "action": "setActiveMenuItem",
                    "target": "ops_item1"
                  },
                  {
                    "action": "showPanel",
                    "target": "service_management_panel"
                  }
                ]
              }
            ]
          }
        ]
      },
      "ops_item2": {
        "onClick": [
          {
            "description": "显示日志管理",
            "cases": [
              {
                "condition": "true",
                "isNewIfGroup": false,
                "actions": [
                  {
                    "action": "setActiveMenuItem",
                    "target": "ops_item2"
                  },
                  {
                    "action": "showPanel",
                    "target": "log_management_panel"
                  }
                ]
              }
            ]
          }
        ]
      },
      "ops_item3": {
        "onClick": [
          {
            "description": "显示备份恢复",
            "cases": [
              {
                "condition": "true",
                "isNewIfGroup": false,
                "actions": [
                  {
                    "action": "setActiveMenuItem",
                    "target": "ops_item3"
                  },
                  {
                    "action": "showPanel",
                    "target": "backup_restore_panel"
                  }
                ]
              }
            ]
          }
        ]
      },
      "ops_item4": {
        "onClick": [
          {
            "description": "显示性能监控",
            "cases": [
              {
                "condition": "true",
                "isNewIfGroup": false,
                "actions": [
                  {
                    "action": "setActiveMenuItem",
                    "target": "ops_item4"
                  },
                  {
                    "action": "showPanel",
                    "target": "performance_monitor_panel"
                  }
                ]
              }
            ]
          }
        ]
      }
    },
    "tabbedViews": {},
    "masters": {},
    "objectPaths": {
      "header": {
        "scriptId": "u0"
      },
      "breadcrumb": {
        "scriptId": "u1"
      },
      "page_title": {
        "scriptId": "u2"
      },
      "left_menu": {
        "scriptId": "u3"
      },
      "config_menu": {
        "scriptId": "u4"
      },
      "config_item1": {
        "scriptId": "u5"
      },
      "config_item2": {
        "scriptId": "u6"
      },
      "config_item3": {
        "scriptId": "u7"
      },
      "config_item4": {
        "scriptId": "u8"
      },
      "ops_menu": {
        "scriptId": "u9"
      },
      "ops_item1": {
        "scriptId": "u10"
      },
      "ops_item2": {
        "scriptId": "u11"
      },
      "ops_item3": {
        "scriptId": "u12"
      },
      "ops_item4": {
        "scriptId": "u13"
      },
      "config_content": {
        "scriptId": "u14"
      },
      "basic_config": {
        "scriptId": "u15"
      }
    },
    "defaultAdaptiveView": "桌面"
  }
});

// 页面加载完成后的初始化
$(document).ready(function() {
  // 初始化菜单状态
  initializeMenu();

  // 绑定事件处理器
  bindEventHandlers();

  // 加载默认配置数据
  loadDefaultConfig();

  // 初始化服务状态监控
  initializeServiceMonitoring();
});

// 初始化菜单
function initializeMenu() {
  // 设置默认选中项
  $('.menu_item').removeClass('selected');
  $('#config_item1').addClass('selected');

  // 显示默认面板
  showConfigPanel('basic');
}

// 绑定事件处理器
function bindEventHandlers() {
  // 菜单项点击事件
  $('.menu_item').click(function() {
    var itemId = $(this).attr('id');
    setActiveMenuItem(itemId);

    // 根据菜单项显示对应面板
    switch(itemId) {
      case 'config_item1':
        showConfigPanel('basic');
        break;
      case 'config_item2':
        showConfigPanel('database');
        break;
      case 'config_item3':
        showConfigPanel('network');
        break;
      case 'config_item4':
        showConfigPanel('security');
        break;
      case 'ops_item1':
        showOpsPanel('service');
        break;
      case 'ops_item2':
        showOpsPanel('log');
        break;
      case 'ops_item3':
        showOpsPanel('backup');
        break;
      case 'ops_item4':
        showOpsPanel('monitor');
        break;
    }
  });

  // 表单提交事件
  $(document).on('click', '.btn-save', function() {
    saveConfiguration();
  });

  // 重置表单事件
  $(document).on('click', '.btn-reset', function() {
    resetConfiguration();
  });
}

// 设置活动菜单项
function setActiveMenuItem(itemId) {
  $('.menu_item').removeClass('selected');
  $('#' + itemId).addClass('selected');
}

// 显示配置面板
function showConfigPanel(type) {
  // 隐藏所有面板
  $('.config-panel').hide();

  // 显示指定面板
  $('#' + type + '_config_panel').show().addClass('fade-in');

  // 更新面板标题
  updatePanelTitle(type);
}

// 显示运维操作面板
function showOpsPanel(type) {
  // 隐藏所有面板
  $('.config-panel').hide();

  // 显示指定面板
  $('#' + type + '_ops_panel').show().addClass('fade-in');

  // 更新面板标题
  updatePanelTitle(type);
}

// 更新面板标题
function updatePanelTitle(type) {
  var titles = {
    'basic': '基础参数配置',
    'database': '数据库配置',
    'network': '网络配置',
    'security': '安全配置',
    'service': '服务管理',
    'log': '日志管理',
    'backup': '备份恢复',
    'monitor': '性能监控'
  };

  $('#config_section_title_text p span').text(titles[type] || '系统配置');
}

// 加载默认配置
function loadDefaultConfig() {
  // 模拟从服务器加载配置数据
  var defaultConfig = {
    systemName: '物联网支持系统',
    systemVersion: 'v3.0.0',
    dataRetention: 365,
    maxConnections: 1000,
    sessionTimeout: 30,
    logLevel: 'INFO'
  };

  // 填充表单数据
  populateForm(defaultConfig);
}

// 填充表单数据
function populateForm(config) {
  $('#system_name_input_text p span').text(config.systemName);
  $('#system_version_input_text p span').text(config.systemVersion);
  $('#data_retention_input_text p span').text(config.dataRetention);
}

// 保存配置
function saveConfiguration() {
  // 验证配置
  if (!validateConfiguration()) {
    return;
  }

  // 显示加载状态
  showLoading();

  // 模拟保存操作
  setTimeout(function() {
    hideLoading();
    showMessage('配置保存成功！', 'success');

    // 保存成功后刷新服务状态
    setTimeout(function() {
      refreshServiceStatus();
    }, 1000);
  }, 1000);
}

// 重置配置
function resetConfiguration() {
  if (confirm('确定要重置配置吗？此操作将恢复默认设置。')) {
    loadDefaultConfig();
    showMessage('配置已重置为默认值', 'info');
  }
}

// 显示加载状态
function showLoading() {
  $('#config_content').addClass('loading');
}

// 隐藏加载状态
function hideLoading() {
  $('#config_content').removeClass('loading');
}

// 显示消息提示
function showMessage(message, type) {
  var messageClass = 'success-text';
  if (type === 'error') messageClass = 'error-text';
  if (type === 'info') messageClass = 'help-text';

  var messageHtml = '<div class="message-toast ' + messageClass + '">' + message + '</div>';
  $('body').append(messageHtml);

  // 3秒后自动消失
  setTimeout(function() {
    $('.message-toast').fadeOut(function() {
      $(this).remove();
    });
  }, 3000);
}

// 服务管理功能
function manageService(serviceName, action) {
  showLoading();

  // 模拟服务操作
  setTimeout(function() {
    hideLoading();

    switch(action) {
      case 'restart':
        updateServiceStatus(serviceName, 'online');
        showMessage(serviceName + ' 服务重启成功', 'success');
        break;
      case 'stop':
        updateServiceStatus(serviceName, 'offline');
        showMessage(serviceName + ' 服务已停止', 'info');
        break;
      case 'backup':
        showMessage(serviceName + ' 备份操作已启动', 'success');
        break;
    }
  }, 2000);
}

// 更新服务状态
function updateServiceStatus(serviceName, status) {
  var statusClass = 'status-' + status;
  var statusText = status === 'online' ? '运行中' :
                   status === 'offline' ? '已停止' : '警告';

  $('#' + serviceName + '_indicator')
    .removeClass('status-online status-offline status-warning')
    .addClass(statusClass);

  $('#' + serviceName + '_status_text_text p span').text(statusText);
}

// 测试连接功能
function testConnection() {
  showLoading();

  // 模拟连接测试
  setTimeout(function() {
    hideLoading();

    var mqttServer = $('#mqtt_server_input_text p span').text();
    var blockchainNode = $('#blockchain_node_input_text p span').text();

    if (mqttServer && blockchainNode) {
      showMessage('连接测试成功！所有服务连接正常。', 'success');
    } else {
      showMessage('连接测试失败，请检查配置参数。', 'error');
    }
  }, 3000);
}

// 初始化服务状态监控
function initializeServiceMonitoring() {
  // 绑定服务操作按钮事件
  $(document).on('click', '#restart_data_collection', function() {
    manageService('data_collection', 'restart');
  });

  $(document).on('click', '#stop_data_collection', function() {
    manageService('data_collection', 'stop');
  });

  $(document).on('click', '#restart_blockchain', function() {
    manageService('blockchain', 'restart');
  });

  $(document).on('click', '#stop_blockchain', function() {
    manageService('blockchain', 'stop');
  });

  $(document).on('click', '#restart_api', function() {
    manageService('api', 'restart');
  });

  $(document).on('click', '#stop_api', function() {
    manageService('api', 'stop');
  });

  $(document).on('click', '#restart_database', function() {
    manageService('database', 'restart');
  });

  $(document).on('click', '#backup_database', function() {
    manageService('database', 'backup');
  });

  // 绑定测试连接按钮
  $(document).on('click', '#test_connection_button', function() {
    testConnection();
  });

  // 定期刷新服务状态（每30秒）
  setInterval(function() {
    refreshServiceStatus();
  }, 30000);
}

// 刷新服务状态
function refreshServiceStatus() {
  // 模拟从服务器获取最新状态
  var services = ['data_collection', 'blockchain', 'api', 'database'];
  var statuses = ['online', 'online', 'warning', 'online'];

  services.forEach(function(service, index) {
    updateServiceStatus(service, statuses[index]);
  });
}

// 配置验证功能
function validateConfiguration() {
  var isValid = true;
  var errors = [];

  // 验证系统名称
  var systemName = $('#system_name_input_text p span').text().trim();
  if (!systemName || systemName === '请输入') {
    errors.push('系统名称不能为空');
    isValid = false;
  }

  // 验证数据保留天数
  var dataRetention = parseInt($('#data_retention_input_text p span').text());
  if (isNaN(dataRetention) || dataRetention < 1 || dataRetention > 3650) {
    errors.push('数据保留天数必须在1-3650之间');
    isValid = false;
  }

  // 验证最大连接数
  var maxConnections = parseInt($('#max_connections_input_text p span').text());
  if (isNaN(maxConnections) || maxConnections < 1 || maxConnections > 10000) {
    errors.push('最大连接数必须在1-10000之间');
    isValid = false;
  }

  // 验证MQTT服务器地址
  var mqttServer = $('#mqtt_server_input_text p span').text().trim();
  if (!mqttServer || !mqttServer.startsWith('mqtt://')) {
    errors.push('MQTT服务器地址格式不正确');
    isValid = false;
  }

  // 验证区块链节点地址
  var blockchainNode = $('#blockchain_node_input_text p span').text().trim();
  if (!blockchainNode || !blockchainNode.startsWith('http')) {
    errors.push('区块链节点地址格式不正确');
    isValid = false;
  }

  if (!isValid) {
    showMessage('配置验证失败：' + errors.join('；'), 'error');
  }

  return isValid;
}
