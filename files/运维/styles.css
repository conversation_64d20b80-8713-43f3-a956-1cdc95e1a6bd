/* 运维管理页面样式 */

/* 基础布局 */
#base {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部样式 */
#header {
  background-color: #ffffff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#header_bg {
  background-color: #ffffff;
  width: 100%;
  height: 80px;
}

/* 面包屑导航 */
#breadcrumb {
  font-size: 14px;
  color: #666;
  line-height: 32px;
}

/* 页面标题 */
#page_title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  line-height: 32px;
}

/* 左侧菜单样式 */
#left_menu {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

#menu_bg {
  background-color: #ffffff;
  border-radius: 8px;
  width: 280px;
  height: 600px;
}

/* 菜单标题 */
.heading_2 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

/* 菜单项样式 */
.menu_item {
  padding: 8px 16px;
  margin: 4px 0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #666;
}

.menu_item:hover {
  background-color: #f0f9ff;
  color: #1890ff;
}

.menu_item.selected {
  background-color: #e6f7ff;
  color: #1890ff;
  border-left: 3px solid #1890ff;
}

/* 右侧内容区域 */
#config_content {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-left: 20px;
}

#content_bg {
  background-color: #ffffff;
  border-radius: 8px;
  width: 1092px;
  height: 600px;
}

/* 配置表单样式 */
#config_section_title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #1890ff;
}

/* 配置组样式 */
#system_config_group {
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

#system_config_title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

/* 表单项样式 */
.form_group {
  margin-bottom: 20px;
}

.form_label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

/* 输入框样式 */
.shape {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  transition: border-color 0.3s ease;
}

.shape:hover {
  border-color: #40a9ff;
}

.shape:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: #1890ff;
  color: #ffffff;
}

.btn-primary:hover {
  background-color: #40a9ff;
}

.btn-secondary {
  background-color: #ffffff;
  color: #666;
  border: 1px solid #d9d9d9;
}

.btn-secondary:hover {
  color: #1890ff;
  border-color: #1890ff;
}

.btn-danger {
  background-color: #ff4d4f;
  color: #ffffff;
}

.btn-danger:hover {
  background-color: #ff7875;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-online {
  background-color: #52c41a;
}

.status-offline {
  background-color: #ff4d4f;
}

.status-warning {
  background-color: #faad14;
}

/* 卡片样式 */
.config-card {
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.config-card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

/* 操作按钮组 */
.action-buttons {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}

.action-buttons .btn {
  margin-left: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  #left_menu {
    width: 240px;
  }

  #config_content {
    width: calc(100% - 260px);
  }
}

@media (max-width: 768px) {
  #left_menu {
    width: 100%;
    margin-bottom: 20px;
  }

  #config_content {
    width: 100%;
    margin-left: 0;
  }

  .form_group {
    width: 100%;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 提示信息 */
.help-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.error-text {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 4px;
}

.success-text {
  font-size: 12px;
  color: #52c41a;
  margin-top: 4px;
}

/* 服务状态监控面板 */
#service_status_panel {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-top: 20px;
}

#service_status_bg {
  background-color: #ffffff;
  border-radius: 8px;
  width: 1392px;
  height: 200px;
}

/* 服务状态卡片 */
.service-card {
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  margin-right: 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  display: inline-block;
  vertical-align: top;
}

.service-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.service-card:last-child {
  margin-right: 0;
}

/* 服务状态指示器 */
.service-card .status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
}

/* 服务操作按钮 */
.service-card .btn {
  padding: 4px 12px;
  font-size: 12px;
  margin-right: 8px;
  margin-top: 8px;
}

.service-card .btn:last-child {
  margin-right: 0;
}

/* 消息提示 */
.message-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1000;
  font-size: 14px;
  max-width: 300px;
}

.message-toast.success-text {
  border-color: #52c41a;
  background-color: #f6ffed;
  color: #52c41a;
}

.message-toast.error-text {
  border-color: #ff4d4f;
  background-color: #fff2f0;
  color: #ff4d4f;
}

.message-toast.help-text {
  border-color: #1890ff;
  background-color: #e6f7ff;
  color: #1890ff;
}

/* 配置组样式增强 */
#iot_config_group {
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

#iot_config_title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

/* 表单布局优化 */
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-col {
  flex: 1;
}

.form-col-half {
  flex: 0 0 48%;
}

/* 输入框组合样式 */
.input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-group .shape {
  flex: 1;
}

.input-group .unit {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}
