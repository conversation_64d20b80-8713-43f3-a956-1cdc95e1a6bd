<!DOCTYPE html>
<html>
<head>
    <title>运维管理演示</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .demo-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .demo-header p {
            color: #666;
            font-size: 16px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .feature-card {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        .feature-card h3 {
            color: #1890ff;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 5px 0;
            color: #666;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #52c41a;
            font-weight: bold;
        }
        .demo-actions {
            text-align: center;
            margin-top: 40px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
        }
        .btn-secondary:hover {
            background-color: #e0e0e0;
        }
        .status-demo {
            margin-top: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 6px;
        }
        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-online { background-color: #52c41a; }
        .status-warning { background-color: #faad14; }
        .status-offline { background-color: #ff4d4f; }
        .tech-stack {
            margin-top: 40px;
            padding: 20px;
            background: #e6f7ff;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }
        .tech-stack h3 {
            margin-top: 0;
            color: #1890ff;
        }
        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .tech-tag {
            background: #1890ff;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🔧 系统运维管理页面</h1>
            <p>提供系统参数配置界面，支持远程运维操作，降低运维成本</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>📊 系统配置管理</h3>
                <ul class="feature-list">
                    <li>基础参数配置（系统名称、版本、数据保留策略）</li>
                    <li>数据库连接配置</li>
                    <li>网络接口设置</li>
                    <li>安全认证参数</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>🌐 物联网配置</h3>
                <ul class="feature-list">
                    <li>MQTT服务器配置</li>
                    <li>数据采集频率设置</li>
                    <li>区块链节点连接</li>
                    <li>数据验证间隔配置</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>⚙️ 运维操作</h3>
                <ul class="feature-list">
                    <li>服务启动/停止/重启</li>
                    <li>系统日志管理</li>
                    <li>数据备份恢复</li>
                    <li>性能监控报告</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>📈 实时监控</h3>
                <ul class="feature-list">
                    <li>服务状态实时显示</li>
                    <li>系统性能指标</li>
                    <li>告警信息推送</li>
                    <li>操作日志记录</li>
                </ul>
            </div>
        </div>

        <div class="status-demo">
            <h3>🚦 服务状态演示</h3>
            <div class="status-item">
                <div class="status-indicator status-online"></div>
                <span>数据采集服务 - 运行中</span>
            </div>
            <div class="status-item">
                <div class="status-indicator status-online"></div>
                <span>区块链服务 - 运行中</span>
            </div>
            <div class="status-item">
                <div class="status-indicator status-warning"></div>
                <span>API服务 - 警告状态</span>
            </div>
            <div class="status-item">
                <div class="status-indicator status-online"></div>
                <span>数据库服务 - 运行中</span>
            </div>
        </div>

        <div class="tech-stack">
            <h3>🛠️ 技术栈</h3>
            <p>基于现代Web技术构建，确保良好的用户体验和系统性能</p>
            <div class="tech-tags">
                <span class="tech-tag">HTML5</span>
                <span class="tech-tag">CSS3</span>
                <span class="tech-tag">JavaScript</span>
                <span class="tech-tag">jQuery</span>
                <span class="tech-tag">Axure RP</span>
                <span class="tech-tag">响应式设计</span>
                <span class="tech-tag">RESTful API</span>
                <span class="tech-tag">实时监控</span>
            </div>
        </div>

        <div class="demo-actions">
            <a href="运维.html" class="btn btn-primary">🚀 打开运维页面</a>
            <a href="运维页面说明.md" class="btn btn-secondary">📖 查看详细说明</a>
            <button class="btn btn-secondary" onclick="showFeatures()">✨ 功能演示</button>
        </div>
    </div>

    <script>
        function showFeatures() {
            alert('运维管理页面主要功能：\n\n' +
                  '1. 系统参数配置 - 统一管理系统各项配置参数\n' +
                  '2. 物联网设置 - 配置MQTT、区块链等物联网相关参数\n' +
                  '3. 服务管理 - 远程控制各种系统服务的启停\n' +
                  '4. 实时监控 - 监控服务状态和系统性能\n' +
                  '5. 操作审计 - 记录所有运维操作日志\n\n' +
                  '点击"打开运维页面"体验完整功能！');
        }

        // 模拟实时状态更新
        setInterval(function() {
            const indicators = document.querySelectorAll('.status-indicator');
            indicators.forEach(function(indicator) {
                if (Math.random() > 0.9) {
                    indicator.style.opacity = '0.5';
                    setTimeout(function() {
                        indicator.style.opacity = '1';
                    }, 200);
                }
            });
        }, 3000);
    </script>
</body>
</html>
