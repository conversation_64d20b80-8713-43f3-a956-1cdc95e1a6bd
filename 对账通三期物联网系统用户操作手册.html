<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对账通三期物联网系统用户操作手册</title>
    <style>
        /* 页面设置 */
        @page {
            size: 21cm 29.7cm;
            margin: 2cm;
        }

        body {
            font-family: "SimSun", "宋体", serif;
            font-size: 10.5pt; /* 五号字体 */
            line-height: 1.5;
            color: black;
            text-align: left;
            margin: 0;
            padding: 20px;
            background-color: white;
        }

        /* 标题样式 */
        h1 {
            font-family: "SimSun", "宋体", serif;
            font-size: 14pt; /* 四号字体 */
            font-weight: bold;
            color: black;
            text-align: center;
            margin: 20px 0;
            page-break-before: auto;
        }

        h2 {
            font-family: "SimSun", "宋体", serif;
            font-size: 12pt; /* 小四号字体 */
            font-weight: bold;
            color: black;
            margin: 15px 0 10px 0;
        }

        h3 {
            font-family: "SimSun", "宋体", serif;
            font-size: 12pt; /* 小四号字体 */
            font-weight: normal;
            color: black;
            margin: 12px 0 8px 0;
        }

        h4 {
            font-family: "SimSun", "宋体", serif;
            font-size: 12pt; /* 小四号字体 */
            font-weight: normal;
            color: black;
            margin: 10px 0 6px 0;
        }

        /* 正文样式 */
        p {
            font-family: "SimSun", "宋体", serif;
            font-size: 10.5pt; /* 五号字体 */
            color: black;
            text-indent: 2em; /* 首行缩进2个字符 */
            margin: 6px 0;
            line-height: 1.5;
        }

        /* 列表样式 */
        ul, ol {
            font-family: "SimSun", "宋体", serif;
            font-size: 10.5pt;
            color: black;
            margin: 6px 0;
            padding-left: 2em;
        }

        li {
            margin: 3px 0;
            line-height: 1.5;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px auto;
            font-family: "SimSun", "宋体", serif;
            font-size: 10.5pt;
            text-align: center;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }

        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }

        /* 图片样式 */
        img {
            display: block;
            margin: 10px auto;
            max-width: 100%;
            height: auto;
        }

        /* 目录样式 */
        .toc {
            margin: 20px 0;
        }

        .toc-item {
            margin: 5px 0;
            text-indent: 0;
        }

        .toc-level-1 {
            font-weight: bold;
        }

        .toc-level-2 {
            margin-left: 2em;
        }

        .toc-level-3 {
            margin-left: 4em;
        }

        /* 封面样式 */
        .cover {
            text-align: center;
            page-break-after: always;
            margin-top: 5cm;
        }

        .cover h1 {
            font-size: 24pt;
            margin: 2cm 0;
        }

        .cover .subtitle {
            font-size: 18pt;
            margin: 1cm 0;
        }

        .cover .info {
            font-size: 14pt;
            margin: 0.5cm 0;
            text-indent: 0;
        }

        /* 分页 */
        .page-break {
            page-break-before: always;
        }

        /* 注意事项样式 */
        .note {
            background-color: #f9f9f9;
            border-left: 4px solid #007acc;
            padding: 10px;
            margin: 10px 0;
        }

        .note p {
            text-indent: 0;
            margin: 0;
        }

        /* 步骤编号 */
        .step {
            font-weight: bold;
            color: #007acc;
        }
    </style>
</head>
<body>
    <!-- 封面 -->
    <div class="cover">
        <h1>对账通三期物联网系统</h1>
        <div class="subtitle">用户操作手册</div>
        <div style="margin-top: 3cm;">
            <p class="info">版本：V3.0.0</p>
            <p class="info">编制日期：2024年12月</p>
            <p class="info">适用范围：物联网支持系统用户</p>
        </div>
    </div>

    <!-- 目录 -->
    <div class="page-break">
        <h1>目录</h1>
        <div class="toc">
            <div class="toc-item toc-level-1">1. 系统概述 ......................................................... 3</div>
            <div class="toc-item toc-level-2">1.1 系统简介 .................................................... 3</div>
            <div class="toc-item toc-level-2">1.2 系统架构 .................................................... 3</div>
            <div class="toc-item toc-level-2">1.3 功能模块 .................................................... 4</div>
            <div class="toc-item toc-level-1">2. 系统登录 ......................................................... 5</div>
            <div class="toc-item toc-level-2">2.1 登录页面 .................................................... 5</div>
            <div class="toc-item toc-level-2">2.2 登录操作 .................................................... 5</div>
            <div class="toc-item toc-level-1">3. 物联平台管理 ..................................................... 6</div>
            <div class="toc-item toc-level-2">3.1 平台概览 .................................................... 6</div>
            <div class="toc-item toc-level-2">3.2 设备管理 .................................................... 6</div>
            <div class="toc-item toc-level-2">3.3 围栏管理 .................................................... 7</div>
            <div class="toc-item toc-level-1">4. 数据采集管理 ..................................................... 8</div>
            <div class="toc-item toc-level-2">4.1 采集数据记录 ................................................ 8</div>
            <div class="toc-item toc-level-2">4.2 数据查询与筛选 .............................................. 9</div>
            <div class="toc-item toc-level-1">5. 数据处理管理 .................................................... 10</div>
            <div class="toc-item toc-level-2">5.1 数据处理记录 ............................................... 10</div>
            <div class="toc-item toc-level-2">5.2 处理状态监控 ............................................... 10</div>
            <div class="toc-item toc-level-1">6. 数据提取管理 .................................................... 11</div>
            <div class="toc-item toc-level-2">6.1 数据提取记录 ............................................... 11</div>
            <div class="toc-item toc-level-2">6.2 提取规则配置 ............................................... 11</div>
            <div class="toc-item toc-level-1">7. 数据推送管理 .................................................... 12</div>
            <div class="toc-item toc-level-2">7.1 数据推送记录 ............................................... 12</div>
            <div class="toc-item toc-level-2">7.2 推送配置管理 ............................................... 12</div>
            <div class="toc-item toc-level-1">8. 业务规则管理 .................................................... 13</div>
            <div class="toc-item toc-level-2">8.1 规则配置 ................................................... 13</div>
            <div class="toc-item toc-level-2">8.2 规则执行监控 ............................................... 14</div>
            <div class="toc-item toc-level-1">9. 定位管理 ........................................................ 15</div>
            <div class="toc-item toc-level-2">9.1 电子围栏设置 ............................................... 15</div>
            <div class="toc-item toc-level-2">9.2 定位监控 ................................................... 15</div>
            <div class="toc-item toc-level-1">10. 用户中心 ....................................................... 16</div>
            <div class="toc-item toc-level-2">10.1 用户管理 .................................................. 16</div>
            <div class="toc-item toc-level-2">10.2 权限配置 .................................................. 16</div>
            <div class="toc-item toc-level-1">11. 系统监控 ....................................................... 17</div>
            <div class="toc-item toc-level-2">11.1 系统状态监控 .............................................. 17</div>
            <div class="toc-item toc-level-2">11.2 性能监控 .................................................. 17</div>
            <div class="toc-item toc-level-1">12. 运维管理 ....................................................... 18</div>
            <div class="toc-item toc-level-2">12.1 系统配置 .................................................. 18</div>
            <div class="toc-item toc-level-2">12.2 运维操作 .................................................. 19</div>
            <div class="toc-item toc-level-1">13. 常见问题解答 ................................................... 20</div>
            <div class="toc-item toc-level-1">14. 附录 ........................................................... 21</div>
        </div>
    </div>

    <!-- 正文开始 -->
    <div class="page-break">
        <h1>1. 系统概述</h1>

        <h2>1.1 系统简介</h2>
        <p>对账通三期物联网系统是基于区块链技术的物联网支持平台，旨在为企业提供完整的物联网数据采集、处理、存储和应用解决方案。系统支持多种类型传感器的接入，实现数据的实时采集、安全传输、智能处理和业务应用。</p>

        <p>系统采用先进的区块链技术确保数据传输的安全性和可追溯性，通过标准化的API接口实现与外部系统的无缝对接，为用户提供高效、可靠的物联网数据服务。</p>

        <h2>1.2 系统架构</h2>
        <p>系统采用分层架构设计，主要包括以下几个层次：</p>
        <ul>
            <li>设备接入层：支持多种物联网设备和传感器的接入</li>
            <li>数据传输层：基于标准物联网通信协议的安全数据传输</li>
            <li>数据处理层：数据清洗、校验、存储和处理</li>
            <li>业务应用层：业务规则管理、数据提取和推送</li>
            <li>系统管理层：用户管理、权限控制、系统监控和运维</li>
        </ul>

        <h2>1.3 功能模块</h2>
        <p>系统主要功能模块包括：</p>

        <table>
            <tr>
                <th>模块名称</th>
                <th>主要功能</th>
                <th>适用用户</th>
            </tr>
            <tr>
                <td>物联平台</td>
                <td>设备管理、状态监控</td>
                <td>系统管理员、运维人员</td>
            </tr>
            <tr>
                <td>数据采集</td>
                <td>传感器数据采集、记录管理</td>
                <td>数据管理员、业务用户</td>
            </tr>
            <tr>
                <td>数据处理</td>
                <td>数据清洗、校验、处理</td>
                <td>数据分析师、技术人员</td>
            </tr>
            <tr>
                <td>数据提取</td>
                <td>按需数据提取、导出</td>
                <td>业务用户、数据分析师</td>
            </tr>
            <tr>
                <td>数据推送</td>
                <td>数据推送、API对接</td>
                <td>系统集成人员、开发人员</td>
            </tr>
            <tr>
                <td>业务规则管理</td>
                <td>业务规则配置、执行</td>
                <td>业务管理员、规则配置员</td>
            </tr>
            <tr>
                <td>定位管理</td>
                <td>电子围栏、定位监控</td>
                <td>安全管理员、监控人员</td>
            </tr>
            <tr>
                <td>用户中心</td>
                <td>用户管理、权限配置</td>
                <td>系统管理员</td>
            </tr>
            <tr>
                <td>系统监控</td>
                <td>系统状态、性能监控</td>
                <td>运维人员、系统管理员</td>
            </tr>
            <tr>
                <td>运维管理</td>
                <td>系统配置、运维操作</td>
                <td>运维人员、技术支持</td>
            </tr>
        </table>
    </div>

    <!-- 系统登录 -->
    <div class="page-break">
        <h1>2. 系统登录</h1>

        <h2>2.1 登录页面</h2>
        <p>系统登录是用户访问物联网平台的入口，确保系统安全性和用户身份验证。</p>

        <h3>2.1.1 页面访问</h3>
        <p>用户可通过以下方式访问登录页面：</p>
        <ul>
            <li>在浏览器地址栏输入系统URL</li>
            <li>点击系统快捷方式</li>
            <li>通过企业门户网站链接访问</li>
        </ul>

        <h3>2.1.2 页面元素</h3>
        <p>登录页面包含以下主要元素：</p>
        <ul>
            <li>系统标题和Logo</li>
            <li>用户名输入框</li>
            <li>密码输入框</li>
            <li>验证码输入框（如启用）</li>
            <li>登录按钮</li>
            <li>忘记密码链接</li>
        </ul>

        <h2>2.2 登录操作</h2>

        <h3>2.2.1 登录步骤</h3>
        <p><span class="step">步骤1：</span>在用户名输入框中输入您的用户名或邮箱地址。</p>
        <p><span class="step">步骤2：</span>在密码输入框中输入您的登录密码。</p>
        <p><span class="step">步骤3：</span>如果系统启用了验证码，请输入页面显示的验证码。</p>
        <p><span class="step">步骤4：</span>点击"登录"按钮完成登录。</p>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 用户名和密码区分大小写，请确保输入正确</p>
            <p>• 连续登录失败3次将锁定账户30分钟</p>
            <p>• 建议使用Chrome、Firefox等现代浏览器访问系统</p>
            <p>• 首次登录建议修改默认密码</p>
        </div>

        <h3>2.2.2 登录异常处理</h3>
        <p>如遇到登录问题，请按以下方式处理：</p>

        <table>
            <tr>
                <th>问题类型</th>
                <th>可能原因</th>
                <th>解决方案</th>
            </tr>
            <tr>
                <td>用户名或密码错误</td>
                <td>输入信息有误</td>
                <td>检查用户名和密码，注意大小写</td>
            </tr>
            <tr>
                <td>账户被锁定</td>
                <td>多次登录失败</td>
                <td>等待30分钟后重试或联系管理员</td>
            </tr>
            <tr>
                <td>验证码错误</td>
                <td>验证码输入错误或过期</td>
                <td>刷新验证码重新输入</td>
            </tr>
            <tr>
                <td>页面无法访问</td>
                <td>网络连接问题</td>
                <td>检查网络连接或联系技术支持</td>
            </tr>
        </table>
    </div>

    <!-- 物联平台管理 -->
    <div class="page-break">
        <h1>3. 物联平台管理</h1>

        <h2>3.1 平台概览</h2>
        <p>物联平台是系统的核心管理界面，提供设备管理、状态监控、数据概览等功能。用户登录成功后，可以通过导航菜单访问物联平台。</p>

        <h3>3.1.1 平台界面</h3>
        <p>物联平台主界面包含以下区域：</p>
        <ul>
            <li>顶部导航栏：系统标题、用户信息、退出按钮</li>
            <li>左侧菜单栏：功能模块导航</li>
            <li>主内容区：显示当前功能页面</li>
            <li>状态栏：系统状态和通知信息</li>
        </ul>

        <h3>3.1.2 平台功能</h3>
        <p>物联平台提供以下主要功能：</p>
        <ul>
            <li>设备接入状态监控</li>
            <li>数据采集实时统计</li>
            <li>系统运行状态展示</li>
            <li>告警信息提醒</li>
            <li>快速操作入口</li>
        </ul>

        <h2>3.2 设备管理</h2>

        <h3>3.2.1 设备列表查看</h3>
        <p><span class="step">步骤1：</span>在左侧导航菜单中点击"物联平台"。</p>
        <p><span class="step">步骤2：</span>系统将显示已接入的设备列表。</p>
        <p><span class="step">步骤3：</span>可以查看设备的基本信息，包括：</p>
        <ul>
            <li>设备编号</li>
            <li>设备名称</li>
            <li>设备类型</li>
            <li>在线状态</li>
            <li>最后通信时间</li>
            <li>数据采集状态</li>
        </ul>

        <h3>3.2.2 设备状态监控</h3>
        <p>系统提供实时的设备状态监控功能：</p>

        <table>
            <tr>
                <th>状态类型</th>
                <th>状态说明</th>
                <th>显示颜色</th>
                <th>处理建议</th>
            </tr>
            <tr>
                <td>在线</td>
                <td>设备正常连接，数据正常</td>
                <td>绿色</td>
                <td>正常监控</td>
            </tr>
            <tr>
                <td>离线</td>
                <td>设备失去连接</td>
                <td>红色</td>
                <td>检查设备和网络连接</td>
            </tr>
            <tr>
                <td>异常</td>
                <td>设备连接但数据异常</td>
                <td>黄色</td>
                <td>检查设备传感器状态</td>
            </tr>
            <tr>
                <td>维护</td>
                <td>设备处于维护状态</td>
                <td>蓝色</td>
                <td>等待维护完成</td>
            </tr>
        </table>

        <h3>3.2.3 设备操作</h3>
        <p>对于已接入的设备，用户可以执行以下操作：</p>

        <h4>3.2.3.1 查看设备详情</h4>
        <p><span class="step">步骤1：</span>在设备列表中点击目标设备。</p>
        <p><span class="step">步骤2：</span>系统将显示设备详细信息页面。</p>
        <p><span class="step">步骤3：</span>可以查看设备的详细配置、历史数据、状态变化等信息。</p>

        <h4>3.2.3.2 设备配置修改</h4>
        <p><span class="step">步骤1：</span>在设备详情页面点击"编辑"按钮。</p>
        <p><span class="step">步骤2：</span>修改设备的相关配置参数。</p>
        <p><span class="step">步骤3：</span>点击"保存"按钮确认修改。</p>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 设备配置修改需要相应权限</p>
            <p>• 修改配置前请确认设备处于可配置状态</p>
            <p>• 重要配置修改建议在维护窗口期间进行</p>
        </div>

        <h2>3.3 围栏管理</h2>
        <p>围栏管理功能集成在物联平台中，提供电子围栏的创建、编辑、监控和管理功能，支持对设备和人员的位置进行实时监控。</p>

        <h3>3.3.1 访问围栏管理界面</h3>
        <p><span class="step">步骤1：</span>在物联平台主界面中，点击地图区域或围栏管理按钮。</p>
        <p><span class="step">步骤2：</span>系统将显示地图界面，展示当前所有已创建的围栏。</p>
        <p><span class="step">步骤3：</span>在右侧面板可以查看围栏列表和详细信息。</p>

        <h3>3.3.2 围栏界面元素</h3>
        <p>围栏管理界面包含以下主要元素：</p>
        <ul>
            <li>地图显示区域：显示地理位置和围栏图形</li>
            <li>围栏列表面板：显示所有围栏的状态信息</li>
            <li>围栏控制按钮：开启/关闭围栏监控</li>
            <li>设备位置标识：显示设备实时位置</li>
            <li>围栏状态指示：不同颜色表示不同状态</li>
        </ul>

        <h3>3.3.3 创建新围栏</h3>
        <p><span class="step">步骤1：</span>在围栏管理界面点击"新建围栏"或"+"按钮。</p>
        <p><span class="step">步骤2：</span>选择围栏类型：</p>
        <ul>
            <li>圆形围栏：适用于定点区域监控</li>
            <li>矩形围栏：适用于规则区域监控</li>
            <li>多边形围栏：适用于不规则区域监控</li>
            <li>线性围栏：适用于路径或边界监控</li>
        </ul>
        <p><span class="step">步骤3：</span>在地图上绘制围栏：</p>
        <ul>
            <li>点击地图确定围栏中心点或起始点</li>
            <li>拖拽调整围栏大小和形状</li>
            <li>对于多边形围栏，依次点击各个顶点</li>
            <li>双击完成围栏绘制</li>
        </ul>
        <p><span class="step">步骤4：</span>配置围栏属性：</p>
        <ul>
            <li>围栏名称：设置便于识别的名称</li>
            <li>围栏描述：添加围栏用途说明</li>
            <li>监控对象：选择需要监控的设备或人员</li>
            <li>告警设置：配置进入/离开告警规则</li>
        </ul>
        <p><span class="step">步骤5：</span>点击"保存"按钮完成围栏创建。</p>

        <h3>3.3.4 围栏状态管理</h3>
        <p>系统通过不同颜色和状态标识来显示围栏的工作状态：</p>

        <table>
            <tr>
                <th>围栏颜色</th>
                <th>状态说明</th>
                <th>监控状态</th>
                <th>操作建议</th>
            </tr>
            <tr>
                <td>绿色</td>
                <td>围栏正常工作</td>
                <td>监控开启</td>
                <td>正常监控</td>
            </tr>
            <tr>
                <td>蓝色</td>
                <td>围栏已创建但未启用</td>
                <td>监控关闭</td>
                <td>可手动启用</td>
            </tr>
            <tr>
                <td>黄色</td>
                <td>围栏有设备进入或离开</td>
                <td>触发告警</td>
                <td>查看告警详情</td>
            </tr>
            <tr>
                <td>红色</td>
                <td>围栏异常或故障</td>
                <td>监控异常</td>
                <td>检查围栏配置</td>
            </tr>
            <tr>
                <td>紫色</td>
                <td>特殊用途围栏</td>
                <td>特殊监控</td>
                <td>按特殊规则处理</td>
            </tr>
        </table>

        <h3>3.3.5 围栏操作管理</h3>

        <h4>3.3.5.1 启用/禁用围栏</h4>
        <p><span class="step">步骤1：</span>在围栏列表中找到目标围栏。</p>
        <p><span class="step">步骤2：</span>点击围栏右侧的开关按钮。</p>
        <p><span class="step">步骤3：</span>确认操作，围栏状态将立即生效。</p>

        <h4>3.3.5.2 编辑围栏</h4>
        <p><span class="step">步骤1：</span>点击地图上的围栏或在列表中选择围栏。</p>
        <p><span class="step">步骤2：</span>点击"编辑"按钮进入编辑模式。</p>
        <p><span class="step">步骤3：</span>可以调整围栏形状、大小和位置。</p>
        <p><span class="step">步骤4：</span>修改围栏属性和监控设置。</p>
        <p><span class="step">步骤5：</span>点击"保存"确认修改。</p>

        <h4>3.3.5.3 删除围栏</h4>
        <p><span class="step">步骤1：</span>选择需要删除的围栏。</p>
        <p><span class="step">步骤2：</span>点击"删除"按钮。</p>
        <p><span class="step">步骤3：</span>确认删除操作，围栏将被永久删除。</p>

        <h3>3.3.6 围栏监控数据</h3>
        <p>围栏管理提供详细的监控数据统计：</p>
        <ul>
            <li>进入/离开事件记录</li>
            <li>设备在围栏内的停留时间</li>
            <li>围栏触发频次统计</li>
            <li>异常事件告警记录</li>
            <li>围栏使用率分析</li>
        </ul>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 围栏创建后需要启用才能进行监控</p>
            <p>• 围栏形状调整时请确保覆盖目标区域</p>
            <p>• 建议为重要围栏设置多重告警机制</p>
            <p>• 定期检查围栏的监控效果和准确性</p>
        </div>
    </div>

    <!-- 数据采集管理 -->
    <div class="page-break">
        <h1>4. 数据采集管理</h1>

        <h2>4.1 采集数据记录</h2>
        <p>数据采集管理模块负责管理所有传感器的数据采集记录，提供数据查询、筛选、导出等功能。</p>

        <h3>4.1.1 访问采集数据记录</h3>
        <p><span class="step">步骤1：</span>在左侧导航菜单中点击"采集数据记录"。</p>
        <p><span class="step">步骤2：</span>系统将显示数据采集记录列表页面。</p>
        <p><span class="step">步骤3：</span>页面将展示最近的数据采集记录。</p>

        <h3>4.1.2 数据记录信息</h3>
        <p>每条采集数据记录包含以下信息：</p>
        <ul>
            <li>设备编号：数据来源设备的唯一标识</li>
            <li>传感器类型：温度、湿度、压力、位置等</li>
            <li>采集时间：数据采集的具体时间</li>
            <li>数据值：传感器采集的具体数值</li>
            <li>数据单位：数据的计量单位</li>
            <li>数据状态：正常、异常、待处理等</li>
            <li>区块链哈希：数据的区块链验证哈希值</li>
        </ul>

        <h2>4.2 数据查询与筛选</h2>

        <h3>4.2.1 基础查询</h3>
        <p><span class="step">步骤1：</span>在页面顶部找到查询条件区域。</p>
        <p><span class="step">步骤2：</span>设置查询条件：</p>
        <ul>
            <li>时间范围：选择开始时间和结束时间</li>
            <li>设备编号：输入特定设备编号</li>
            <li>传感器类型：选择传感器类型</li>
            <li>数据状态：选择数据状态</li>
        </ul>
        <p><span class="step">步骤3：</span>点击"查询"按钮执行查询。</p>

        <h3>4.2.2 高级筛选</h3>
        <p>系统提供高级筛选功能，支持更复杂的查询条件：</p>

        <table>
            <tr>
                <th>筛选条件</th>
                <th>操作方式</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>数据值范围</td>
                <td>输入最小值和最大值</td>
                <td>筛选指定数值范围内的数据</td>
            </tr>
            <tr>
                <td>设备状态</td>
                <td>选择设备在线状态</td>
                <td>筛选特定状态设备的数据</td>
            </tr>
            <tr>
                <td>数据质量</td>
                <td>选择数据质量等级</td>
                <td>筛选高质量或异常数据</td>
            </tr>
            <tr>
                <td>区块链验证</td>
                <td>选择验证状态</td>
                <td>筛选已验证或待验证数据</td>
            </tr>
        </table>

        <h3>4.2.3 数据导出</h3>
        <p><span class="step">步骤1：</span>在查询结果页面，选择需要导出的数据记录。</p>
        <p><span class="step">步骤2：</span>点击"导出"按钮。</p>
        <p><span class="step">步骤3：</span>选择导出格式（Excel、CSV、PDF）。</p>
        <p><span class="step">步骤4：</span>确认导出设置，点击"确定"开始导出。</p>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 大量数据导出可能需要较长时间，请耐心等待</p>
            <p>• 导出的数据文件将保存到浏览器默认下载目录</p>
            <p>• 建议根据实际需要设置合理的时间范围，避免导出过多数据</p>
        </div>
    </div>

    <!-- 数据处理管理 -->
    <div class="page-break">
        <h1>5. 数据处理管理</h1>

        <h2>5.1 数据处理记录</h2>
        <p>数据处理管理模块负责监控和管理数据清洗、校验、转换等处理过程，确保数据质量和处理效率。</p>

        <h3>5.1.1 访问数据处理记录</h3>
        <p><span class="step">步骤1：</span>在左侧导航菜单中点击"数据处理记录"。</p>
        <p><span class="step">步骤2：</span>系统将显示数据处理记录列表。</p>
        <p><span class="step">步骤3：</span>可以查看各个处理任务的执行状态和结果。</p>

        <h3>5.1.2 处理记录信息</h3>
        <p>每条数据处理记录包含以下信息：</p>
        <ul>
            <li>处理任务ID：处理任务的唯一标识</li>
            <li>数据源：原始数据来源信息</li>
            <li>处理类型：清洗、校验、转换、聚合等</li>
            <li>开始时间：处理任务开始时间</li>
            <li>结束时间：处理任务完成时间</li>
            <li>处理状态：进行中、已完成、失败、暂停</li>
            <li>处理结果：成功处理的数据量和错误信息</li>
            <li>区块链记录：处理过程的区块链时间戳</li>
        </ul>

        <h2>5.2 处理状态监控</h2>

        <h3>5.2.1 实时监控</h3>
        <p>系统提供实时的数据处理状态监控：</p>

        <table>
            <tr>
                <th>处理状态</th>
                <th>状态说明</th>
                <th>显示标识</th>
                <th>操作建议</th>
            </tr>
            <tr>
                <td>等待中</td>
                <td>任务已提交，等待执行</td>
                <td>灰色圆点</td>
                <td>正常等待</td>
            </tr>
            <tr>
                <td>进行中</td>
                <td>任务正在执行处理</td>
                <td>蓝色进度条</td>
                <td>监控进度</td>
            </tr>
            <tr>
                <td>已完成</td>
                <td>任务成功完成</td>
                <td>绿色对勾</td>
                <td>查看结果</td>
            </tr>
            <tr>
                <td>失败</td>
                <td>任务执行失败</td>
                <td>红色叉号</td>
                <td>查看错误日志</td>
            </tr>
            <tr>
                <td>暂停</td>
                <td>任务被手动暂停</td>
                <td>黄色暂停符</td>
                <td>可恢复执行</td>
            </tr>
        </table>

        <h3>5.2.2 处理任务操作</h3>
        <p>用户可以对处理任务执行以下操作：</p>

        <h4>5.2.2.1 查看任务详情</h4>
        <p><span class="step">步骤1：</span>在处理记录列表中点击目标任务。</p>
        <p><span class="step">步骤2：</span>系统将显示任务详细信息页面。</p>
        <p><span class="step">步骤3：</span>可以查看处理参数、执行日志、错误信息等。</p>

        <h4>5.2.2.2 重新执行任务</h4>
        <p><span class="step">步骤1：</span>选择失败或需要重新执行的任务。</p>
        <p><span class="step">步骤2：</span>点击"重新执行"按钮。</p>
        <p><span class="step">步骤3：</span>确认重新执行参数，点击"确定"。</p>

        <h4>5.2.2.3 暂停和恢复任务</h4>
        <p><span class="step">步骤1：</span>选择正在执行的任务。</p>
        <p><span class="step">步骤2：</span>点击"暂停"按钮暂停任务执行。</p>
        <p><span class="step">步骤3：</span>需要恢复时，点击"恢复"按钮继续执行。</p>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 数据处理任务一旦开始执行，建议不要随意中断</p>
            <p>• 处理失败的任务请查看错误日志，排除问题后重新执行</p>
            <p>• 大批量数据处理建议在系统负载较低时进行</p>
            <p>• 重要数据处理前建议先进行小批量测试</p>
        </div>
    </div>

    <!-- 数据提取管理 -->
    <div class="page-break">
        <h1>6. 数据提取管理</h1>

        <h2>6.1 数据提取记录</h2>
        <p>数据提取管理模块提供按业务规则进行数据定制处理和提取的功能，支持提取具体工作的开始时间、结束时间、时长等业务数据。</p>

        <h3>6.1.1 访问数据提取记录</h3>
        <p><span class="step">步骤1：</span>在左侧导航菜单中点击"数据提取记录"。</p>
        <p><span class="step">步骤2：</span>系统将显示数据提取任务列表。</p>
        <p><span class="step">步骤3：</span>可以查看提取任务的执行状态和结果。</p>

        <h3>6.1.2 提取记录信息</h3>
        <p>每条数据提取记录包含以下信息：</p>
        <ul>
            <li>提取任务ID：提取任务的唯一标识</li>
            <li>业务规则：应用的业务规则名称</li>
            <li>数据源范围：提取数据的时间范围和设备范围</li>
            <li>提取类型：工作时长、设备状态、异常事件等</li>
            <li>创建时间：提取任务创建时间</li>
            <li>执行状态：待执行、执行中、已完成、失败</li>
            <li>提取结果：成功提取的数据量和文件信息</li>
        </ul>

        <h2>6.2 提取规则配置</h2>

        <h3>6.2.1 创建提取任务</h3>
        <p><span class="step">步骤1：</span>在数据提取记录页面点击"新建提取任务"按钮。</p>
        <p><span class="step">步骤2：</span>配置提取参数：</p>
        <ul>
            <li>任务名称：为提取任务设置名称</li>
            <li>业务规则：选择适用的业务规则</li>
            <li>数据源：选择数据来源设备和时间范围</li>
            <li>提取内容：选择需要提取的数据类型</li>
            <li>输出格式：选择输出文件格式</li>
        </ul>
        <p><span class="step">步骤3：</span>点击"创建任务"按钮提交提取任务。</p>

        <h3>6.2.2 提取规则类型</h3>
        <p>系统支持多种数据提取规则：</p>

        <table>
            <tr>
                <th>规则类型</th>
                <th>提取内容</th>
                <th>应用场景</th>
            </tr>
            <tr>
                <td>工作时长提取</td>
                <td>设备工作开始时间、结束时间、总时长</td>
                <td>设备使用统计、工时计算</td>
            </tr>
            <tr>
                <td>异常事件提取</td>
                <td>异常发生时间、类型、持续时间</td>
                <td>故障分析、维护计划</td>
            </tr>
            <tr>
                <td>性能数据提取</td>
                <td>设备性能指标、效率数据</td>
                <td>性能分析、优化建议</td>
            </tr>
            <tr>
                <td>位置轨迹提取</td>
                <td>设备或人员的位置变化轨迹</td>
                <td>路径分析、区域统计</td>
            </tr>
        </table>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 数据提取任务的执行时间取决于数据量大小</p>
            <p>• 建议在业务低峰期执行大批量数据提取</p>
            <p>• 提取结果文件有效期为30天，请及时下载</p>
        </div>
    </div>

    <!-- 数据推送管理 -->
    <div class="page-break">
        <h1>7. 数据推送管理</h1>

        <h2>7.1 数据推送记录</h2>
        <p>数据推送管理模块负责将处理后的业务数据通过API接口推送到上层应用系统，实现系统间的数据集成。</p>

        <h3>7.1.1 访问数据推送记录</h3>
        <p><span class="step">步骤1：</span>在左侧导航菜单中点击"数据推送记录"。</p>
        <p><span class="step">步骤2：</span>系统将显示数据推送任务列表。</p>
        <p><span class="step">步骤3：</span>可以查看推送任务的执行状态和结果。</p>

        <h3>7.1.2 推送记录信息</h3>
        <p>每条数据推送记录包含以下信息：</p>
        <ul>
            <li>推送任务ID：推送任务的唯一标识</li>
            <li>目标系统：数据推送的目标应用系统</li>
            <li>推送接口：使用的API接口地址</li>
            <li>数据类型：推送的数据类型和格式</li>
            <li>推送时间：数据推送的执行时间</li>
            <li>推送状态：成功、失败、重试中</li>
            <li>推送结果：推送的数据量和响应信息</li>
        </ul>

        <h2>7.2 推送配置管理</h2>

        <h3>7.2.1 配置推送接口</h3>
        <p><span class="step">步骤1：</span>在数据推送记录页面点击"推送配置"按钮。</p>
        <p><span class="step">步骤2：</span>配置推送接口参数：</p>
        <ul>
            <li>接口名称：为推送接口设置名称</li>
            <li>目标URL：目标系统的API接口地址</li>
            <li>认证方式：API Key、Token、用户名密码等</li>
            <li>数据格式：JSON、XML、CSV等</li>
            <li>推送频率：实时、定时、手动触发</li>
        </ul>
        <p><span class="step">步骤3：</span>点击"测试连接"验证接口配置。</p>
        <p><span class="step">步骤4：</span>点击"保存配置"完成接口配置。</p>

        <h3>7.2.2 推送策略设置</h3>
        <p>系统支持多种数据推送策略：</p>

        <table>
            <tr>
                <th>推送策略</th>
                <th>触发条件</th>
                <th>适用场景</th>
            </tr>
            <tr>
                <td>实时推送</td>
                <td>数据处理完成后立即推送</td>
                <td>实时监控、紧急告警</td>
            </tr>
            <tr>
                <td>定时推送</td>
                <td>按设定时间间隔推送</td>
                <td>定期报表、批量同步</td>
            </tr>
            <tr>
                <td>阈值推送</td>
                <td>数据量达到设定阈值时推送</td>
                <td>批量处理、性能优化</td>
            </tr>
            <tr>
                <td>手动推送</td>
                <td>用户手动触发推送</td>
                <td>特殊需求、测试验证</td>
            </tr>
        </table>

        <h3>7.2.3 推送失败处理</h3>
        <p>当数据推送失败时，系统提供以下处理机制：</p>
        <ul>
            <li>自动重试：系统自动重试推送，最多重试3次</li>
            <li>失败告警：推送失败时发送告警通知</li>
            <li>数据缓存：失败的数据暂存，等待重新推送</li>
            <li>手动重推：用户可手动重新推送失败的数据</li>
        </ul>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 推送接口配置前请确认目标系统的接口规范</p>
            <p>• 建议定期检查推送接口的连通性</p>
            <p>• 重要数据推送建议启用推送确认机制</p>
        </div>
    </div>

    <!-- 业务规则管理 -->
    <div class="page-break">
        <h1>8. 业务规则管理</h1>

        <h2>8.1 规则配置</h2>
        <p>业务规则管理模块支持定义工作业务规则、异常排除规则等，为数据处理和提取提供业务逻辑支持。</p>

        <h3>8.1.1 访问业务规则管理</h3>
        <p><span class="step">步骤1：</span>在左侧导航菜单中点击"业务规则管理"。</p>
        <p><span class="step">步骤2：</span>系统将显示已配置的业务规则列表。</p>
        <p><span class="step">步骤3：</span>可以查看、编辑、删除现有规则。</p>

        <h3>8.1.2 创建业务规则</h3>
        <p><span class="step">步骤1：</span>在业务规则管理页面点击"新建规则"按钮。</p>
        <p><span class="step">步骤2：</span>配置规则基本信息：</p>
        <ul>
            <li>规则名称：为规则设置易识别的名称</li>
            <li>规则类型：工作规则、异常规则、计算规则等</li>
            <li>适用范围：指定规则适用的设备或数据类型</li>
            <li>规则描述：详细描述规则的用途和逻辑</li>
        </ul>
        <p><span class="step">步骤3：</span>配置规则条件和动作。</p>
        <p><span class="step">步骤4：</span>点击"保存规则"完成创建。</p>

        <h3>8.1.3 规则类型说明</h3>
        <p>系统支持多种类型的业务规则：</p>

        <table>
            <tr>
                <th>规则类型</th>
                <th>功能说明</th>
                <th>配置要点</th>
            </tr>
            <tr>
                <td>工作时间规则</td>
                <td>定义设备工作时间的判断标准</td>
                <td>设置工作状态阈值、时间窗口</td>
            </tr>
            <tr>
                <td>异常检测规则</td>
                <td>识别和排除异常数据</td>
                <td>设置异常值范围、检测算法</td>
            </tr>
            <tr>
                <td>数据清洗规则</td>
                <td>数据预处理和清洗逻辑</td>
                <td>设置过滤条件、转换规则</td>
            </tr>
            <tr>
                <td>计算规则</td>
                <td>数据计算和聚合逻辑</td>
                <td>设置计算公式、聚合方式</td>
            </tr>
        </table>

        <h2>8.2 规则执行监控</h2>

        <h3>8.2.1 规则执行状态</h3>
        <p>系统提供业务规则执行状态的实时监控：</p>
        <ul>
            <li>规则执行次数统计</li>
            <li>规则执行成功率</li>
            <li>规则执行耗时分析</li>
            <li>规则异常情况记录</li>
        </ul>

        <h3>8.2.2 规则性能优化</h3>
        <p>为确保规则执行效率，建议定期进行以下优化：</p>
        <ul>
            <li>检查规则条件的复杂度</li>
            <li>优化规则执行顺序</li>
            <li>清理无效或重复的规则</li>
            <li>监控规则执行的资源消耗</li>
        </ul>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 业务规则修改前请充分测试，避免影响数据处理</p>
            <p>• 建议为重要规则设置备份</p>
            <p>• 规则删除前请确认没有其他模块依赖</p>
        </div>
    </div>

    <!-- 定位管理 -->
    <div class="page-break">
        <h1>9. 定位管理</h1>

        <h2>9.1 电子围栏设置</h2>
        <p>定位管理模块提供电子围栏设置和定位监控功能，支持对设备和人员的位置进行实时监控和管理。</p>

        <h3>9.1.1 访问定位管理</h3>
        <p><span class="step">步骤1：</span>在左侧导航菜单中点击"定位管理"。</p>
        <p><span class="step">步骤2：</span>系统将显示地图界面和围栏管理功能。</p>
        <p><span class="step">步骤3：</span>可以查看现有围栏和设备位置信息。</p>

        <h3>9.1.2 创建电子围栏</h3>
        <p><span class="step">步骤1：</span>在定位管理页面点击"新建围栏"按钮。</p>
        <p><span class="step">步骤2：</span>在地图上绘制围栏区域：</p>
        <ul>
            <li>选择围栏形状：圆形、矩形、多边形</li>
            <li>在地图上标记围栏边界点</li>
            <li>调整围栏大小和位置</li>
        </ul>
        <p><span class="step">步骤3：</span>配置围栏属性：</p>
        <ul>
            <li>围栏名称：设置围栏的名称</li>
            <li>围栏类型：工作区域、禁止区域、安全区域</li>
            <li>监控对象：选择需要监控的设备或人员</li>
            <li>告警设置：进入/离开告警、停留时间告警</li>
        </ul>
        <p><span class="step">步骤4：</span>点击"保存围栏"完成创建。</p>

        <h2>9.2 定位监控</h2>

        <h3>9.2.1 实时位置监控</h3>
        <p>系统提供实时的位置监控功能：</p>
        <ul>
            <li>设备实时位置显示</li>
            <li>移动轨迹记录</li>
            <li>围栏状态监控</li>
            <li>异常位置告警</li>
        </ul>

        <h3>9.2.2 位置数据分析</h3>
        <p>系统支持对位置数据进行分析：</p>

        <table>
            <tr>
                <th>分析类型</th>
                <th>分析内容</th>
                <th>应用价值</th>
            </tr>
            <tr>
                <td>轨迹分析</td>
                <td>移动路径、停留点、移动速度</td>
                <td>工作效率分析、路径优化</td>
            </tr>
            <tr>
                <td>区域分析</td>
                <td>区域停留时间、进出频次</td>
                <td>区域利用率、安全管理</td>
            </tr>
            <tr>
                <td>异常分析</td>
                <td>异常位置、违规行为</td>
                <td>安全监控、合规检查</td>
            </tr>
        </table>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 定位功能需要设备支持GPS或其他定位技术</p>
            <p>• 室内定位精度可能受环境影响</p>
            <p>• 建议定期校准定位设备</p>
        </div>
    </div>

    <!-- 用户中心 -->
    <div class="page-break">
        <h1>10. 用户中心</h1>

        <h2>10.1 用户管理</h2>
        <p>用户中心提供用户账户管理、权限配置、角色分配等功能，确保系统的安全性和可管理性。</p>

        <h3>10.1.1 访问用户中心</h3>
        <p><span class="step">步骤1：</span>在左侧导航菜单中点击"用户中心"。</p>
        <p><span class="step">步骤2：</span>系统将显示用户管理界面。</p>
        <p><span class="step">步骤3：</span>可以查看用户列表和用户详细信息。</p>

        <h3>10.1.2 创建用户账户</h3>
        <p><span class="step">步骤1：</span>在用户中心页面点击"新建用户"按钮。</p>
        <p><span class="step">步骤2：</span>填写用户基本信息：</p>
        <ul>
            <li>用户名：设置用户登录名</li>
            <li>姓名：用户真实姓名</li>
            <li>邮箱：用户邮箱地址</li>
            <li>手机号：用户联系电话</li>
            <li>部门：用户所属部门</li>
        </ul>
        <p><span class="step">步骤3：</span>设置用户权限和角色。</p>
        <p><span class="step">步骤4：</span>点击"创建用户"完成账户创建。</p>

        <h2>10.2 权限配置</h2>

        <h3>10.2.1 角色管理</h3>
        <p>系统预定义了以下用户角色：</p>

        <table>
            <tr>
                <th>角色名称</th>
                <th>权限范围</th>
                <th>适用人员</th>
            </tr>
            <tr>
                <td>系统管理员</td>
                <td>所有功能的完全访问权限</td>
                <td>系统管理人员</td>
            </tr>
            <tr>
                <td>数据管理员</td>
                <td>数据采集、处理、提取功能</td>
                <td>数据管理人员</td>
            </tr>
            <tr>
                <td>业务用户</td>
                <td>数据查询、报表查看功能</td>
                <td>业务部门用户</td>
            </tr>
            <tr>
                <td>运维人员</td>
                <td>系统监控、运维管理功能</td>
                <td>技术运维人员</td>
            </tr>
            <tr>
                <td>只读用户</td>
                <td>仅查看权限，无修改权限</td>
                <td>临时访问用户</td>
            </tr>
        </table>

        <h3>10.2.2 权限分配</h3>
        <p><span class="step">步骤1：</span>选择需要配置权限的用户。</p>
        <p><span class="step">步骤2：</span>在权限配置页面选择用户角色。</p>
        <p><span class="step">步骤3：</span>根据需要调整具体功能权限。</p>
        <p><span class="step">步骤4：</span>点击"保存权限"完成配置。</p>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 权限配置遵循最小权限原则</p>
            <p>• 重要权限变更需要审批流程</p>
            <p>• 建议定期审查用户权限</p>
        </div>
    </div>

    <!-- 系统监控 -->
    <div class="page-break">
        <h1>11. 系统监控</h1>

        <h2>11.1 系统状态监控</h2>
        <p>系统监控模块提供实时的系统状态监控、性能分析和告警管理功能，确保系统稳定运行。</p>

        <h3>11.1.1 访问系统监控</h3>
        <p><span class="step">步骤1：</span>在左侧导航菜单中点击"系统监控"。</p>
        <p><span class="step">步骤2：</span>系统将显示监控仪表板。</p>
        <p><span class="step">步骤3：</span>可以查看各项系统指标和状态信息。</p>

        <h3>11.1.2 监控指标</h3>
        <p>系统监控包含以下主要指标：</p>
        <ul>
            <li>系统资源使用率：CPU、内存、磁盘、网络</li>
            <li>数据库性能：连接数、查询响应时间、事务处理量</li>
            <li>应用服务状态：服务可用性、响应时间、错误率</li>
            <li>设备连接状态：在线设备数、数据采集频率、通信质量</li>
            <li>业务处理指标：数据处理量、任务执行状态、处理耗时</li>
        </ul>

        <h2>11.2 性能监控</h2>

        <h3>11.2.1 性能指标分析</h3>
        <p>系统提供详细的性能分析功能：</p>

        <table>
            <tr>
                <th>监控类别</th>
                <th>关键指标</th>
                <th>正常范围</th>
                <th>告警阈值</th>
            </tr>
            <tr>
                <td>系统资源</td>
                <td>CPU使用率</td>
                <td>< 70%</td>
                <td>> 85%</td>
            </tr>
            <tr>
                <td>系统资源</td>
                <td>内存使用率</td>
                <td>< 80%</td>
                <td>> 90%</td>
            </tr>
            <tr>
                <td>数据库</td>
                <td>查询响应时间</td>
                <td>< 100ms</td>
                <td>> 500ms</td>
            </tr>
            <tr>
                <td>网络</td>
                <td>网络延迟</td>
                <td>< 50ms</td>
                <td>> 200ms</td>
            </tr>
            <tr>
                <td>业务处理</td>
                <td>数据处理成功率</td>
                <td>> 95%</td>
                <td>< 90%</td>
            </tr>
        </table>

        <h3>11.2.2 告警管理</h3>
        <p><span class="step">步骤1：</span>在系统监控页面点击"告警设置"。</p>
        <p><span class="step">步骤2：</span>配置告警规则：</p>
        <ul>
            <li>告警指标：选择需要监控的指标</li>
            <li>阈值设置：设置告警触发条件</li>
            <li>告警级别：设置告警严重程度</li>
            <li>通知方式：邮件、短信、系统通知</li>
        </ul>
        <p><span class="step">步骤3：</span>点击"保存设置"完成告警配置。</p>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 建议根据业务需求合理设置告警阈值</p>
            <p>• 重要告警建议设置多种通知方式</p>
            <p>• 定期检查告警规则的有效性</p>
        </div>
    </div>

    <!-- 运维管理 -->
    <div class="page-break">
        <h1>12. 运维管理</h1>

        <h2>12.1 系统配置</h2>
        <p>运维管理模块提供系统配置、维护操作、日志管理等功能，支持系统的日常运维工作。</p>

        <h3>12.1.1 访问运维管理</h3>
        <p><span class="step">步骤1：</span>在左侧导航菜单中点击"运维"。</p>
        <p><span class="step">步骤2：</span>系统将显示运维管理界面。</p>
        <p><span class="step">步骤3：</span>可以查看系统配置和执行运维操作。</p>

        <h3>12.1.2 系统参数配置</h3>
        <p>系统配置包含以下主要参数：</p>
        <ul>
            <li>数据采集配置：采集频率、数据格式、存储策略</li>
            <li>处理引擎配置：处理线程数、批处理大小、超时设置</li>
            <li>存储配置：数据库连接、备份策略、清理规则</li>
            <li>网络配置：API接口、通信协议、安全设置</li>
            <li>告警配置：告警规则、通知设置、升级策略</li>
        </ul>

        <h2>12.2 运维操作</h2>

        <h3>12.2.1 系统维护</h3>
        <p>系统提供以下维护操作：</p>

        <table>
            <tr>
                <th>维护类型</th>
                <th>操作内容</th>
                <th>执行频率</th>
                <th>注意事项</th>
            </tr>
            <tr>
                <td>数据备份</td>
                <td>数据库备份、配置文件备份</td>
                <td>每日</td>
                <td>确认备份完整性</td>
            </tr>
            <tr>
                <td>日志清理</td>
                <td>清理过期日志文件</td>
                <td>每周</td>
                <td>保留必要的历史日志</td>
            </tr>
            <tr>
                <td>性能优化</td>
                <td>数据库优化、缓存清理</td>
                <td>每月</td>
                <td>在业务低峰期执行</td>
            </tr>
            <tr>
                <td>安全检查</td>
                <td>权限审查、漏洞扫描</td>
                <td>每季度</td>
                <td>及时修复发现的问题</td>
            </tr>
        </table>

        <h3>12.2.2 日志管理</h3>
        <p><span class="step">步骤1：</span>在运维管理页面点击"日志管理"。</p>
        <p><span class="step">步骤2：</span>选择日志类型：</p>
        <ul>
            <li>系统日志：系统运行日志</li>
            <li>应用日志：应用程序日志</li>
            <li>错误日志：错误和异常日志</li>
            <li>访问日志：用户访问日志</li>
            <li>操作日志：用户操作记录</li>
        </ul>
        <p><span class="step">步骤3：</span>设置查询条件，查看或下载日志文件。</p>

        <div class="note">
            <p><strong>注意事项：</strong></p>
            <p>• 重要运维操作前请做好备份</p>
            <p>• 运维操作建议在维护窗口期间进行</p>
            <p>• 保持运维操作记录的完整性</p>
        </div>
    </div>

    <!-- 常见问题解答 -->
    <div class="page-break">
        <h1>13. 常见问题解答</h1>

        <h2>13.1 登录问题</h2>
        <p><strong>Q：忘记登录密码怎么办？</strong></p>
        <p>A：请联系系统管理员重置密码，或使用"忘记密码"功能通过邮箱重置。</p>

        <p><strong>Q：账户被锁定如何解决？</strong></p>
        <p>A：等待30分钟自动解锁，或联系管理员手动解锁账户。</p>

        <h2>13.2 数据问题</h2>
        <p><strong>Q：数据采集异常如何处理？</strong></p>
        <p>A：首先检查设备连接状态，然后查看数据采集日志，确认设备配置是否正确。</p>

        <p><strong>Q：数据处理失败怎么办？</strong></p>
        <p>A：查看处理任务的错误日志，根据错误信息调整处理参数，然后重新执行任务。</p>

        <h2>13.3 系统问题</h2>
        <p><strong>Q：系统响应缓慢如何优化？</strong></p>
        <p>A：检查系统资源使用情况，清理不必要的数据，优化查询条件，必要时联系技术支持。</p>

        <p><strong>Q：如何查看系统运行状态？</strong></p>
        <p>A：访问系统监控页面，查看各项性能指标和告警信息。</p>

        <h2>13.4 权限问题</h2>
        <p><strong>Q：无法访问某个功能模块？</strong></p>
        <p>A：检查用户权限配置，联系管理员分配相应的功能权限。</p>

        <p><strong>Q：如何申请更高权限？</strong></p>
        <p>A：向部门主管或系统管理员提交权限申请，说明业务需求。</p>
    </div>

    <!-- 附录 -->
    <div class="page-break">
        <h1>14. 附录</h1>

        <h2>14.1 系统技术规格</h2>
        <ul>
            <li>支持的浏览器：Chrome 80+、Firefox 75+、Safari 13+、Edge 80+</li>
            <li>推荐屏幕分辨率：1920×1080或更高</li>
            <li>网络要求：稳定的互联网连接，带宽不低于10Mbps</li>
            <li>操作系统：Windows 10、macOS 10.15、Ubuntu 18.04或更高版本</li>
        </ul>

        <h2>14.2 联系方式</h2>
        <ul>
            <li>技术支持热线：400-XXX-XXXX</li>
            <li>技术支持邮箱：<EMAIL></li>
            <li>在线帮助：系统内置帮助文档</li>
            <li>用户社区：https://community.example.com</li>
        </ul>

        <h2>14.3 版本更新记录</h2>
        <table>
            <tr>
                <th>版本号</th>
                <th>更新日期</th>
                <th>主要更新内容</th>
            </tr>
            <tr>
                <td>V3.0.0</td>
                <td>2024年12月</td>
                <td>初始版本，包含所有核心功能模块</td>
            </tr>
            <tr>
                <td>V3.0.1</td>
                <td>待发布</td>
                <td>性能优化和bug修复</td>
            </tr>
        </table>

        <h2>14.4 术语表</h2>
        <ul>
            <li><strong>物联网（IoT）</strong>：物联网是指通过信息传感设备，按约定的协议，将任何物体与网络相连接，物体通过信息传播媒介进行信息交换和通信。</li>
            <li><strong>区块链</strong>：一种分布式数据存储、点对点传输、共识机制、加密算法等计算机技术的新型应用模式。</li>
            <li><strong>API</strong>：应用程序编程接口，是一些预先定义的函数，目的是提供应用程序与开发人员基于某软件或硬件得以访问一组例程的能力。</li>
            <li><strong>电子围栏</strong>：利用GPS定位技术，在电子地图上设定一个虚拟的围栏区域，当目标进入或离开该区域时，系统会自动发出告警信息。</li>
        </ul>

        <div style="text-align: center; margin-top: 3cm;">
            <p>—— 文档结束 ——</p>
            <p style="font-size: 9pt; color: #666;">本文档版权归对账通三期物联网系统所有</p>
        </div>
    </div>
</body>
</html>
