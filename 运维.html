<!DOCTYPE html>
<html>
  <head>
    <title>系统运维管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/运维/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/运维/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- 页面头部导航 -->
      <div id="header" class="ax_default" data-left="0" data-top="0" data-width="1440" data-height="80">
        <div id="header_bg" class="ax_default box_2">
          <div id="header_bg_div" class=""></div>
        </div>

        <!-- 面包屑导航 -->
        <div id="breadcrumb" class="ax_default paragraph" data-left="24" data-top="24" data-width="400" data-height="32">
          <div id="breadcrumb_div" class=""></div>
          <div id="breadcrumb_text" class="text">
            <p><span style="color:rgba(0, 0, 0, 0.447058823529412);">首页 / 系统管理 /</span><span style="color:rgba(0, 0, 0, 0.647058823529412);"> 系统运维</span></p>
          </div>
        </div>

        <!-- 页面标题 -->
        <div id="page_title" class="ax_default heading_1" data-left="24" data-top="48" data-width="200" data-height="32">
          <div id="page_title_div" class=""></div>
          <div id="page_title_text" class="text">
            <p><span>系统运维管理</span></p>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div id="main_content" class="ax_default" data-left="24" data-top="100" data-width="1392" data-height="600">

        <!-- 左侧功能菜单 -->
        <div id="left_menu" class="ax_default" data-left="24" data-top="100" data-width="280" data-height="600">
          <div id="menu_bg" class="ax_default box_2">
            <div id="menu_bg_div" class=""></div>
          </div>

          <!-- 系统配置菜单 -->
          <div id="config_menu" class="ax_default" data-left="32" data-top="120" data-width="264" data-height="200">
            <div id="config_title" class="ax_default heading_2" data-left="32" data-top="120" data-width="100" data-height="24">
              <div id="config_title_div" class=""></div>
              <div id="config_title_text" class="text">
                <p><span>系统配置</span></p>
              </div>
            </div>

            <div id="config_item1" class="ax_default menu_item selected" data-left="40" data-top="152" data-width="240" data-height="32">
              <div id="config_item1_div" class=""></div>
              <div id="config_item1_text" class="text">
                <p><span>基础参数配置</span></p>
              </div>
            </div>

            <div id="config_item2" class="ax_default menu_item" data-left="40" data-top="184" data-width="240" data-height="32">
              <div id="config_item2_div" class=""></div>
              <div id="config_item2_text" class="text">
                <p><span>数据库配置</span></p>
              </div>
            </div>

            <div id="config_item3" class="ax_default menu_item" data-left="40" data-top="216" data-width="240" data-height="32">
              <div id="config_item3_div" class=""></div>
              <div id="config_item3_text" class="text">
                <p><span>网络配置</span></p>
              </div>
            </div>

            <div id="config_item4" class="ax_default menu_item" data-left="40" data-top="248" data-width="240" data-height="32">
              <div id="config_item4_div" class=""></div>
              <div id="config_item4_text" class="text">
                <p><span>安全配置</span></p>
              </div>
            </div>
          </div>

          <!-- 运维操作菜单 -->
          <div id="ops_menu" class="ax_default" data-left="32" data-top="340" data-width="264" data-height="200">
            <div id="ops_title" class="ax_default heading_2" data-left="32" data-top="340" data-width="100" data-height="24">
              <div id="ops_title_div" class=""></div>
              <div id="ops_title_text" class="text">
                <p><span>运维操作</span></p>
              </div>
            </div>

            <div id="ops_item1" class="ax_default menu_item" data-left="40" data-top="372" data-width="240" data-height="32">
              <div id="ops_item1_div" class=""></div>
              <div id="ops_item1_text" class="text">
                <p><span>服务管理</span></p>
              </div>
            </div>

            <div id="ops_item2" class="ax_default menu_item" data-left="40" data-top="404" data-width="240" data-height="32">
              <div id="ops_item2_div" class=""></div>
              <div id="ops_item2_text" class="text">
                <p><span>日志管理</span></p>
              </div>
            </div>

            <div id="ops_item3" class="ax_default menu_item" data-left="40" data-top="436" data-width="240" data-height="32">
              <div id="ops_item3_div" class=""></div>
              <div id="ops_item3_text" class="text">
                <p><span>备份恢复</span></p>
              </div>
            </div>

            <div id="ops_item4" class="ax_default menu_item" data-left="40" data-top="468" data-width="240" data-height="32">
              <div id="ops_item4_div" class=""></div>
              <div id="ops_item4_text" class="text">
                <p><span>性能监控</span></p>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧配置内容区域 -->
        <div id="config_content" class="ax_default" data-left="324" data-top="100" data-width="1092" data-height="600">
          <div id="content_bg" class="ax_default box_2">
            <div id="content_bg_div" class=""></div>
          </div>

          <!-- 基础参数配置表单 -->
          <div id="basic_config" class="ax_default" data-left="344" data-top="120" data-width="1052" data-height="560">

            <!-- 配置标题 -->
            <div id="config_section_title" class="ax_default heading_1" data-left="344" data-top="120" data-width="200" data-height="32">
              <div id="config_section_title_div" class=""></div>
              <div id="config_section_title_text" class="text">
                <p><span>基础参数配置</span></p>
              </div>
            </div>

            <!-- 系统基础配置 -->
            <div id="system_config_group" class="ax_default" data-left="344" data-top="172" data-width="1052" data-height="160">
              <div id="system_config_title" class="ax_default heading_2" data-left="344" data-top="172" data-width="120" data-height="24">
                <div id="system_config_title_div" class=""></div>
                <div id="system_config_title_text" class="text">
                  <p><span>系统基础配置</span></p>
                </div>
              </div>

              <!-- 系统名称 -->
              <div id="system_name_group" class="ax_default" data-left="344" data-top="208" data-width="500" data-height="56">
                <div id="system_name_label" class="ax_default heading_1" data-left="344" data-top="208" data-width="100" data-height="24">
                  <div id="system_name_label_div" class=""></div>
                  <div id="system_name_label_text" class="text">
                    <p><span>系统名称：</span></p>
                  </div>
                </div>
                <div id="system_name_input" class="ax_default shape" data-left="344" data-top="232" data-width="300" data-height="32">
                  <div id="system_name_input_div" class=""></div>
                  <div id="system_name_input_text" class="text">
                    <p><span>物联网支持系统</span></p>
                  </div>
                </div>
              </div>

              <!-- 系统版本 -->
              <div id="system_version_group" class="ax_default" data-left="864" data-top="208" data-width="300" data-height="56">
                <div id="system_version_label" class="ax_default heading_1" data-left="864" data-top="208" data-width="100" data-height="24">
                  <div id="system_version_label_div" class=""></div>
                  <div id="system_version_label_text" class="text">
                    <p><span>系统版本：</span></p>
                  </div>
                </div>
                <div id="system_version_input" class="ax_default shape" data-left="864" data-top="232" data-width="200" data-height="32">
                  <div id="system_version_input_div" class=""></div>
                  <div id="system_version_input_text" class="text">
                    <p><span>v3.0.0</span></p>
                  </div>
                </div>
              </div>

              <!-- 数据保留天数 -->
              <div id="data_retention_group" class="ax_default" data-left="344" data-top="280" data-width="500" data-height="56">
                <div id="data_retention_label" class="ax_default heading_1" data-left="344" data-top="280" data-width="120" data-height="24">
                  <div id="data_retention_label_div" class=""></div>
                  <div id="data_retention_label_text" class="text">
                    <p><span>数据保留天数：</span></p>
                  </div>
                </div>
                <div id="data_retention_input" class="ax_default shape" data-left="344" data-top="304" data-width="150" data-height="32">
                  <div id="data_retention_input_div" class=""></div>
                  <div id="data_retention_input_text" class="text">
                    <p><span>365</span></p>
                  </div>
                </div>
                <div id="data_retention_unit" class="ax_default paragraph" data-left="504" data-top="304" data-width="40" data-height="32">
                  <div id="data_retention_unit_div" class=""></div>
                  <div id="data_retention_unit_text" class="text">
                    <p><span>天</span></p>
                  </div>
                </div>
              </div>

              <!-- 最大连接数 -->
              <div id="max_connections_group" class="ax_default" data-left="864" data-top="280" data-width="300" data-height="56">
                <div id="max_connections_label" class="ax_default heading_1" data-left="864" data-top="280" data-width="100" data-height="24">
                  <div id="max_connections_label_div" class=""></div>
                  <div id="max_connections_label_text" class="text">
                    <p><span>最大连接数：</span></p>
                  </div>
                </div>
                <div id="max_connections_input" class="ax_default shape" data-left="864" data-top="304" data-width="150" data-height="32">
                  <div id="max_connections_input_div" class=""></div>
                  <div id="max_connections_input_text" class="text">
                    <p><span>1000</span></p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 物联网配置 -->
            <div id="iot_config_group" class="ax_default" data-left="344" data-top="352" data-width="1052" data-height="160">
              <div id="iot_config_title" class="ax_default heading_2" data-left="344" data-top="352" data-width="120" data-height="24">
                <div id="iot_config_title_div" class=""></div>
                <div id="iot_config_title_text" class="text">
                  <p><span>物联网配置</span></p>
                </div>
              </div>

              <!-- MQTT服务器地址 -->
              <div id="mqtt_server_group" class="ax_default" data-left="344" data-top="388" data-width="500" data-height="56">
                <div id="mqtt_server_label" class="ax_default heading_1" data-left="344" data-top="388" data-width="120" data-height="24">
                  <div id="mqtt_server_label_div" class=""></div>
                  <div id="mqtt_server_label_text" class="text">
                    <p><span>MQTT服务器：</span></p>
                  </div>
                </div>
                <div id="mqtt_server_input" class="ax_default shape" data-left="344" data-top="412" data-width="300" data-height="32">
                  <div id="mqtt_server_input_div" class=""></div>
                  <div id="mqtt_server_input_text" class="text">
                    <p><span>mqtt://192.168.1.100:1883</span></p>
                  </div>
                </div>
              </div>

              <!-- 数据采集频率 -->
              <div id="collection_frequency_group" class="ax_default" data-left="864" data-top="388" data-width="300" data-height="56">
                <div id="collection_frequency_label" class="ax_default heading_1" data-left="864" data-top="388" data-width="120" data-height="24">
                  <div id="collection_frequency_label_div" class=""></div>
                  <div id="collection_frequency_label_text" class="text">
                    <p><span>采集频率：</span></p>
                  </div>
                </div>
                <div id="collection_frequency_input" class="ax_default shape" data-left="864" data-top="412" data-width="100" data-height="32">
                  <div id="collection_frequency_input_div" class=""></div>
                  <div id="collection_frequency_input_text" class="text">
                    <p><span>30</span></p>
                  </div>
                </div>
                <div id="collection_frequency_unit" class="ax_default paragraph" data-left="974" data-top="412" data-width="40" data-height="32">
                  <div id="collection_frequency_unit_div" class=""></div>
                  <div id="collection_frequency_unit_text" class="text">
                    <p><span>秒</span></p>
                  </div>
                </div>
              </div>

              <!-- 区块链节点地址 -->
              <div id="blockchain_node_group" class="ax_default" data-left="344" data-top="460" data-width="500" data-height="56">
                <div id="blockchain_node_label" class="ax_default heading_1" data-left="344" data-top="460" data-width="120" data-height="24">
                  <div id="blockchain_node_label_div" class=""></div>
                  <div id="blockchain_node_label_text" class="text">
                    <p><span>区块链节点：</span></p>
                  </div>
                </div>
                <div id="blockchain_node_input" class="ax_default shape" data-left="344" data-top="484" data-width="300" data-height="32">
                  <div id="blockchain_node_input_div" class=""></div>
                  <div id="blockchain_node_input_text" class="text">
                    <p><span>http://blockchain.local:8545</span></p>
                  </div>
                </div>
              </div>

              <!-- 数据验证间隔 -->
              <div id="validation_interval_group" class="ax_default" data-left="864" data-top="460" data-width="300" data-height="56">
                <div id="validation_interval_label" class="ax_default heading_1" data-left="864" data-top="460" data-width="120" data-height="24">
                  <div id="validation_interval_label_div" class=""></div>
                  <div id="validation_interval_label_text" class="text">
                    <p><span>验证间隔：</span></p>
                  </div>
                </div>
                <div id="validation_interval_input" class="ax_default shape" data-left="864" data-top="484" data-width="100" data-height="32">
                  <div id="validation_interval_input_div" class=""></div>
                  <div id="validation_interval_input_text" class="text">
                    <p><span>300</span></p>
                  </div>
                </div>
                <div id="validation_interval_unit" class="ax_default paragraph" data-left="974" data-top="484" data-width="40" data-height="32">
                  <div id="validation_interval_unit_div" class=""></div>
                  <div id="validation_interval_unit_text" class="text">
                    <p><span>秒</span></p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div id="action_buttons" class="ax_default" data-left="344" data-top="540" data-width="1052" data-height="48">
              <div id="save_button" class="ax_default btn btn-primary" data-left="1244" data-top="548" data-width="80" data-height="32">
                <div id="save_button_div" class=""></div>
                <div id="save_button_text" class="text">
                  <p><span>保存</span></p>
                </div>
              </div>

              <div id="reset_button" class="ax_default btn btn-secondary" data-left="1144" data-top="548" data-width="80" data-height="32">
                <div id="reset_button_div" class=""></div>
                <div id="reset_button_text" class="text">
                  <p><span>重置</span></p>
                </div>
              </div>

              <div id="test_connection_button" class="ax_default btn btn-secondary" data-left="1024" data-top="548" data-width="100" data-height="32">
                <div id="test_connection_button_div" class=""></div>
                <div id="test_connection_button_text" class="text">
                  <p><span>测试连接</span></p>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <!-- 服务状态监控面板 -->
      <div id="service_status_panel" class="ax_default" data-left="24" data-top="720" data-width="1392" data-height="200">
        <div id="service_status_bg" class="ax_default box_2">
          <div id="service_status_bg_div" class=""></div>
        </div>

        <div id="service_status_title" class="ax_default heading_1" data-left="44" data-top="740" data-width="200" data-height="32">
          <div id="service_status_title_div" class=""></div>
          <div id="service_status_title_text" class="text">
            <p><span>服务状态监控</span></p>
          </div>
        </div>

        <!-- 服务状态卡片 -->
        <div id="service_cards" class="ax_default" data-left="44" data-top="780" data-width="1352" data-height="120">

          <!-- 数据采集服务 -->
          <div id="data_collection_service" class="ax_default service-card" data-left="44" data-top="780" data-width="320" data-height="120">
            <div id="data_collection_bg" class="ax_default box_2">
              <div id="data_collection_bg_div" class=""></div>
            </div>
            <div id="data_collection_title" class="ax_default heading_2" data-left="64" data-top="800" data-width="120" data-height="24">
              <div id="data_collection_title_div" class=""></div>
              <div id="data_collection_title_text" class="text">
                <p><span>数据采集服务</span></p>
              </div>
            </div>
            <div id="data_collection_status" class="ax_default" data-left="64" data-top="830" data-width="100" data-height="20">
              <div id="data_collection_indicator" class="ax_default status-indicator status-online"></div>
              <div id="data_collection_status_text" class="ax_default paragraph" data-left="80" data-top="830" data-width="60" data-height="20">
                <div id="data_collection_status_text_div" class=""></div>
                <div id="data_collection_status_text_text" class="text">
                  <p><span>运行中</span></p>
                </div>
              </div>
            </div>
            <div id="data_collection_actions" class="ax_default" data-left="64" data-top="860" data-width="280" data-height="32">
              <div id="restart_data_collection" class="ax_default btn btn-secondary" data-left="64" data-top="860" data-width="60" data-height="24">
                <div id="restart_data_collection_div" class=""></div>
                <div id="restart_data_collection_text" class="text">
                  <p><span>重启</span></p>
                </div>
              </div>
              <div id="stop_data_collection" class="ax_default btn btn-danger" data-left="134" data-top="860" data-width="60" data-height="24">
                <div id="stop_data_collection_div" class=""></div>
                <div id="stop_data_collection_text" class="text">
                  <p><span>停止</span></p>
                </div>
              </div>
            </div>
          </div>

          <!-- 区块链服务 -->
          <div id="blockchain_service" class="ax_default service-card" data-left="384" data-top="780" data-width="320" data-height="120">
            <div id="blockchain_bg" class="ax_default box_2">
              <div id="blockchain_bg_div" class=""></div>
            </div>
            <div id="blockchain_title" class="ax_default heading_2" data-left="404" data-top="800" data-width="120" data-height="24">
              <div id="blockchain_title_div" class=""></div>
              <div id="blockchain_title_text" class="text">
                <p><span>区块链服务</span></p>
              </div>
            </div>
            <div id="blockchain_status" class="ax_default" data-left="404" data-top="830" data-width="100" data-height="20">
              <div id="blockchain_indicator" class="ax_default status-indicator status-online"></div>
              <div id="blockchain_status_text" class="ax_default paragraph" data-left="420" data-top="830" data-width="60" data-height="20">
                <div id="blockchain_status_text_div" class=""></div>
                <div id="blockchain_status_text_text" class="text">
                  <p><span>运行中</span></p>
                </div>
              </div>
            </div>
            <div id="blockchain_actions" class="ax_default" data-left="404" data-top="860" data-width="280" data-height="32">
              <div id="restart_blockchain" class="ax_default btn btn-secondary" data-left="404" data-top="860" data-width="60" data-height="24">
                <div id="restart_blockchain_div" class=""></div>
                <div id="restart_blockchain_text" class="text">
                  <p><span>重启</span></p>
                </div>
              </div>
              <div id="stop_blockchain" class="ax_default btn btn-danger" data-left="474" data-top="860" data-width="60" data-height="24">
                <div id="stop_blockchain_div" class=""></div>
                <div id="stop_blockchain_text" class="text">
                  <p><span>停止</span></p>
                </div>
              </div>
            </div>
          </div>

          <!-- API服务 -->
          <div id="api_service" class="ax_default service-card" data-left="724" data-top="780" data-width="320" data-height="120">
            <div id="api_bg" class="ax_default box_2">
              <div id="api_bg_div" class=""></div>
            </div>
            <div id="api_title" class="ax_default heading_2" data-left="744" data-top="800" data-width="120" data-height="24">
              <div id="api_title_div" class=""></div>
              <div id="api_title_text" class="text">
                <p><span>API服务</span></p>
              </div>
            </div>
            <div id="api_status" class="ax_default" data-left="744" data-top="830" data-width="100" data-height="20">
              <div id="api_indicator" class="ax_default status-indicator status-warning"></div>
              <div id="api_status_text" class="ax_default paragraph" data-left="760" data-top="830" data-width="60" data-height="20">
                <div id="api_status_text_div" class=""></div>
                <div id="api_status_text_text" class="text">
                  <p><span>警告</span></p>
                </div>
              </div>
            </div>
            <div id="api_actions" class="ax_default" data-left="744" data-top="860" data-width="280" data-height="32">
              <div id="restart_api" class="ax_default btn btn-secondary" data-left="744" data-top="860" data-width="60" data-height="24">
                <div id="restart_api_div" class=""></div>
                <div id="restart_api_text" class="text">
                  <p><span>重启</span></p>
                </div>
              </div>
              <div id="stop_api" class="ax_default btn btn-danger" data-left="814" data-top="860" data-width="60" data-height="24">
                <div id="stop_api_div" class=""></div>
                <div id="stop_api_text" class="text">
                  <p><span>停止</span></p>
                </div>
              </div>
            </div>
          </div>

          <!-- 数据库服务 -->
          <div id="database_service" class="ax_default service-card" data-left="1064" data-top="780" data-width="320" data-height="120">
            <div id="database_bg" class="ax_default box_2">
              <div id="database_bg_div" class=""></div>
            </div>
            <div id="database_title" class="ax_default heading_2" data-left="1084" data-top="800" data-width="120" data-height="24">
              <div id="database_title_div" class=""></div>
              <div id="database_title_text" class="text">
                <p><span>数据库服务</span></p>
              </div>
            </div>
            <div id="database_status" class="ax_default" data-left="1084" data-top="830" data-width="100" data-height="20">
              <div id="database_indicator" class="ax_default status-indicator status-online"></div>
              <div id="database_status_text" class="ax_default paragraph" data-left="1100" data-top="830" data-width="60" data-height="20">
                <div id="database_status_text_div" class=""></div>
                <div id="database_status_text_text" class="text">
                  <p><span>运行中</span></p>
                </div>
              </div>
            </div>
            <div id="database_actions" class="ax_default" data-left="1084" data-top="860" data-width="280" data-height="32">
              <div id="restart_database" class="ax_default btn btn-secondary" data-left="1084" data-top="860" data-width="60" data-height="24">
                <div id="restart_database_div" class=""></div>
                <div id="restart_database_text" class="text">
                  <p><span>重启</span></p>
                </div>
              </div>
              <div id="backup_database" class="ax_default btn btn-primary" data-left="1154" data-top="860" data-width="60" data-height="24">
                <div id="backup_database_div" class=""></div>
                <div id="backup_database_text" class="text">
                  <p><span>备份</span></p>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
